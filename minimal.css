/* Minimal, clean stylesheet for the landing page */

/* 1) Design tokens */
:root {
  --bg-0: #0a0e17;
  --bg-1: #10172a;
  --surface: #1a2440;
  --text: #f0f0f8;
  --muted: #b8c0d8;
  --gold: #d4af37;
  --radius: 12px;
  --radius-sm: 6px;
  --space-1: 0.5rem;
  --space-2: 0.75rem;
  --space-3: 1rem;
  --space-4: 1.5rem;
  --space-5: 2rem;
}

/* 2) Base */
html { box-sizing: border-box; }
*,*::before,*::after { box-sizing: inherit; }

body {
  margin: 0;
  font-family: 'Montserrat', system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
  line-height: 1.6;
  color: var(--text);
  background: radial-gradient(1200px 800px at 10% 0%, #1c2541 0%, var(--bg-1) 50%, var(--bg-0) 100%);
  background-attachment: scroll;
}

@media (prefers-reduced-motion: reduce) {
  * { animation-duration: 0.001ms !important; animation-iteration-count: 1 !important; transition-duration: 0.001ms !important; }
}

img { max-width: 100%; display: block; }
a { color: inherit; text-decoration: none; }

/* 3) Layout */
.container { max-width: 1200px; width: 100%; margin: 0 auto; padding-inline: var(--space-3); }
section { padding-block: var(--space-5); }

header {
  background: linear-gradient(135deg, rgba(42,30,48,.95), rgba(10,14,23,.95));
  border-bottom: 1px solid rgba(212,175,55,0.35);
}

.hero { text-align: center; padding-block: calc(var(--space-5) + var(--space-4)); }
.hero h1 { font-family: 'Playfair Display', serif; font-weight: 800; font-size: clamp(1.75rem, 3vw + 1rem, 3rem); letter-spacing: 0.5px; }
.hero p { color: var(--muted); margin-top: var(--space-2); }

/* 4) Components */
.cta-btn {
  display: inline-block;
  margin-top: var(--space-3);
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius-sm);
  background: var(--gold);
  color: #151515;
  font-weight: 700;
}
.cta-btn:hover { filter: brightness(1.05); }
.cta-btn:focus-visible { outline: 2px solid #fff; outline-offset: 2px; }

.why-choose-us ul { list-style: disc; padding-left: 1.25rem; margin-top: var(--space-2); }
.why-choose-us li { margin-block: 0.35rem; color: var(--text); }

.game-categories { display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: var(--space-4); }
.category {
  background: var(--surface);
  border: 1px solid rgba(255,255,255,.08);
  border-radius: var(--radius);
  padding: var(--space-4);
  text-align: center;
}
.category h3 { font-size: 1.1rem; font-weight: 700; color: var(--gold); }

.bonus-promotions p { color: var(--muted); }

.cta-form {
  background: linear-gradient(135deg, rgba(26,36,64,0.8), rgba(10,14,23,0.8));
  border: 1px solid rgba(212,175,55,0.15);
  border-radius: var(--radius);
  padding: var(--space-5);
}

/* Form */
label { display: block; margin-top: var(--space-2); margin-bottom: 0.25rem; }
input[type="text"], input[type="email"], input[type="tel"] {
  width: 100%;
  padding: 0.65rem 0.8rem;
  border-radius: var(--radius-sm);
  border: 1px solid rgba(255,255,255,0.15);
  background: rgba(255,255,255,0.05);
  color: var(--text);
}
input::placeholder { color: var(--muted); }

#form-message { margin-top: var(--space-2); font-weight: 600; }
#form-message.success { color: #2ecc71; }
#form-message.error { color: #e44242; }

/* Footer */
footer { padding-block: var(--space-4); text-align: center; color: var(--muted); }
footer ul { list-style: none; padding: 0; margin: var(--space-2) 0 0; display: flex; gap: var(--space-3); justify-content: center; flex-wrap: wrap; }
footer a { text-decoration: underline; text-underline-offset: 2px; }

/* 5) Responsive tweaks */
@media (max-width: 768px) {
  .hero { padding-block: var(--space-5); }
  section { padding-block: var(--space-4); }
}

